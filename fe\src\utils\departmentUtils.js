import { DEPARTMENT_IDS } from "../constants/systemConstants";

/**
 * Department utilities for mapping and display
 */

// Create reverse mapping from ID to name
export const DEPARTMENT_ID_TO_NAME = Object.entries(DEPARTMENT_IDS).reduce(
  (acc, [name, id]) => {
    acc[id] = name;           // number key
    acc[String(id)] = name;   // string key
    return acc;
  },
  {}
);


export const getDepartmentNameById = (departmentId) => {
  if (!departmentId) return "Không xác định";
  
  return DEPARTMENT_ID_TO_NAME[departmentId] || 
         DEPARTMENT_ID_TO_NAME[String(departmentId)] || 
         DEPARTMENT_ID_TO_NAME[Number(departmentId)] || 
         `Khoa ${departmentId}`;
};


export const extractDepartmentFromUser = (userData) => {
  if (!userData) return { id: null, name: null };
  
  // Try to get department ID from multiple possible fields
  const departmentId = userData.departmentId || 
                      userData.departmentID || 
                      userData.DepartmentId || 
                      userData.DepartmentID;
  
  // Try to get department name from multiple possible fields
  const departmentName = userData.department || 
                        userData.departmentName || 
                        userData.Department || 
                        userData.DepartmentName;
  
  return {
    id: departmentId,
    name: departmentName && departmentName.trim() ? departmentName.trim() : null
  };
};


export const getDepartmentFromUser = (userData) => {
  const { id, name } = extractDepartmentFromUser(userData);
  
  // If we have department name directly, return it
  if (name) {
    return name;
  }
  
  // If we have department ID, map it to name
  if (id) {
    return getDepartmentNameById(id);
  }
  
  return "Không xác định";
};

export const isBloodDepartmentUser = (userData) => {
  const { id } = extractDepartmentFromUser(userData);
  return id === 1 || id === "1"; // Blood department ID is 1
};


export const getAllDepartments = () => {
  return Object.entries(DEPARTMENT_IDS).map(([name, id]) => ({
    id,
    name
  }));
};

export default {
  DEPARTMENT_ID_TO_NAME,
  getDepartmentNameById,
  extractDepartmentFromUser,
  getDepartmentFromUser,
  isBloodDepartmentUser,
  getAllDepartments
};
