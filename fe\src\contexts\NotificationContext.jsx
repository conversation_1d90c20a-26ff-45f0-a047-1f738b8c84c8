import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { useAuth } from "./AuthContext";
import NotificationService from "../services/notificationService";
import { toast } from "../utils/toastUtils";

const NotificationContext = createContext();

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }
  return context;
};

export const NotificationProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(Date.now()); // Add timestamp for re-render trigger
  const [pollingInterval, setPollingInterval] = useState(null);

  // Load notifications when user changes
  const loadNotifications = useCallback(
    async (forceRefresh = false, silent = false) => {
      if (!isAuthenticated || !user?.id) {
        setNotifications([]);
        setUnreadCount(0);
        return;
      }

      try {
        // Only show loading for initial load or explicit refresh, not for polling
        if (!silent) {
          setLoading(true);
        }

        const response = await NotificationService.getNotifications(user.id);

        if (response && Array.isArray(response)) {
          const filteredNotifications =
            NotificationService.filterActiveNotifications(response);

          // Use a ref to get current notifications to avoid dependency cycle
          setNotifications(currentNotifications => {
            // Check if there are actually new notifications
            const hasNewNotifications = JSON.stringify(filteredNotifications) !== JSON.stringify(currentNotifications);

            if (hasNewNotifications || forceRefresh) {
              console.log("🔔 New notifications detected, updating state");

              // Check for truly new notifications (not just updates)
              if (!forceRefresh && currentNotifications.length > 0) {
                const newNotificationIds = filteredNotifications
                  .filter(newNotif => !currentNotifications.some(oldNotif =>
                    oldNotif.id === newNotif.id ||
                    oldNotif.NotificationID === newNotif.NotificationID
                  ))
                  .map(n => n.id || n.NotificationID);

                if (newNotificationIds.length > 0) {
                  console.log("🔔 Truly new notifications received:", newNotificationIds.length);
                  // Show a subtle notification for new notifications
                  if (silent) {
                    toast.info(`Bạn có ${newNotificationIds.length} thông báo mới`, {
                      position: "top-right",
                      autoClose: 3000,
                    });
                  }
                }
              }

              // Calculate unread count
              const unread = filteredNotifications.filter(
                (n) => !n.isRead && !n.IsRead
              ).length;
              setUnreadCount(unread);
              setLastUpdated(Date.now()); // Trigger component updates

              return filteredNotifications;
            }

            return currentNotifications; // No change
          });
        } else {
          setNotifications([]);
          setUnreadCount(0);
        }
      } catch (error) {
        console.error("Error loading notifications:", error);
        if (!silent) {
          setNotifications([]);
          setUnreadCount(0);
        }
      } finally {
        if (!silent) {
          setLoading(false);
        }
      }
    },
    [isAuthenticated, user?.id] // Removed notifications dependency
  );

  // Load notifications on mount and when user changes
  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  // Setup real-time notification polling and event listeners
  useEffect(() => {
    if (!isAuthenticated || !user?.id) {
      // Clear polling if user is not authenticated
      if (pollingInterval) {
        clearInterval(pollingInterval);
        setPollingInterval(null);
      }
      return;
    }

    // Only setup polling if not already setup
    if (!pollingInterval) {
      console.log("Setting up real-time notification polling for user:", user.id);

      // Setup polling every 15 seconds for new notifications
      const interval = setInterval(() => {
        console.log("Polling for new notifications...");
        loadNotifications(true, true); // silent = true for polling
      }, 15000); // 15 seconds

      setPollingInterval(interval);
    }

    // Setup event listeners for window focus and visibility change
    const handleWindowFocus = () => {
      console.log("Window focused - refreshing notifications");
      loadNotifications(true, true); // silent = true for focus events
    };

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log("Tab became visible - refreshing notifications");
        loadNotifications(true, true); // silent = true for visibility events
      }
    };

    // Add event listeners
    window.addEventListener('focus', handleWindowFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup function
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
        setPollingInterval(null);
      }
      window.removeEventListener('focus', handleWindowFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isAuthenticated, user?.id, loadNotifications]);

  // Mark single notification as read
  const markAsRead = async (notificationId) => {
    try {
      const result = await NotificationService.markAsRead(notificationId);

      if (result.success || result) {
        // Update local state immediately with proper immutability
        setNotifications((prev) =>
          prev.map((notification) => {
            const isTarget =
              notification.id === notificationId ||
              notification.NotificationID === notificationId ||
              notification.notificationId === notificationId ||
              String(notification.id) === String(notificationId) ||
              String(notification.NotificationID) === String(notificationId);

            if (isTarget) {
              return {
                ...notification,
                isRead: true,
                IsRead: true,
                read: true, // Add all possible read flags
              };
            }
            return notification;
          })
        );

        // Update unread count
        setUnreadCount((prev) => {
          const newCount = Math.max(0, prev - 1);
          return newCount;
        });
        toast.success("Đã đánh dấu thông báo đã đọc");
        setLastUpdated(Date.now()); // Trigger component updates

        return true;
      } else {
        toast.error("Không thể đánh dấu thông báo đã đọc");
        return false;
      }
    } catch (error) {
      toast.error("Không thể đánh dấu thông báo đã đọc");
      return false;
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user?.id) return false;

    try {
      // Get all unread notifications
      const unreadNotifications = notifications.filter(
        (n) => !n.isRead && !n.IsRead
      );

      if (unreadNotifications.length === 0) {
        toast.info("Không có thông báo chưa đọc nào");
        return true;
      }

      const result = await NotificationService.markAllAsRead(unreadNotifications);

      if (result.success || result) {
        // Update all notifications to read status immediately
        setNotifications((prev) =>
          prev.map((notification) => ({
            ...notification,
            isRead: true,
            IsRead: true,
          }))
        );

        // Reset unread count to 0
        setUnreadCount(0);

        // Show success message with details if there were any errors
        if (result.errors && result.errors.length > 0) {
          toast.warning(result.message);
        } else {
          toast.success("Đã đánh dấu tất cả thông báo đã đọc");
        }

        setLastUpdated(Date.now()); // Trigger component updates
        return true;
      } else {
        toast.error("Không thể đánh dấu tất cả thông báo đã đọc");
        return false;
      }
    } catch (error) {
      toast.error(error.message || "Không thể đánh dấu tất cả thông báo đã đọc");
      return false;
    }
  };

  // Delete single notification
  const deleteNotification = async (notificationId) => {
    try {
      const result = await NotificationService.deleteNotification(
        notificationId
      );

      if (result.success || result) {
        // Find and remove from local state immediately with better ID matching
        const deletedNotification = notifications.find(
          (n) =>
            n.id === notificationId ||
            n.NotificationID === notificationId ||
            n.notificationId === notificationId ||
            String(n.id) === String(notificationId) ||
            String(n.NotificationID) === String(notificationId)
        );



        setNotifications((prev) =>
          prev.filter(
            (n) =>
              n.id !== notificationId &&
              n.NotificationID !== notificationId &&
              n.notificationId !== notificationId &&
              String(n.id) !== String(notificationId) &&
              String(n.NotificationID) !== String(notificationId)
          )
        );

        // Update unread count if deleted notification was unread
        if (
          deletedNotification &&
          !deletedNotification.isRead &&
          !deletedNotification.IsRead
        ) {
          setUnreadCount((prev) => {
            const newCount = Math.max(0, prev - 1);
            return newCount;
          });
        }
        toast.success("Đã xóa thông báo");
        setLastUpdated(Date.now()); // Trigger component updates

        return true;
      } else {
        toast.error("Không thể xóa thông báo");
        return false;
      }
    } catch (error) {
      toast.error("Không thể xóa thông báo");
      return false;
    }
  };

  // Force refresh notifications from API
  const forceRefresh = useCallback(async () => {
    await loadNotifications(true);
  }, [loadNotifications]);

  // Add new notification (for real-time updates)
  const addNotification = useCallback((newNotification) => {
    setNotifications((prev) => [newNotification, ...prev]);

    // Update unread count if new notification is unread
    if (!newNotification.isRead && !newNotification.IsRead) {
      setUnreadCount((prev) => prev + 1);
    }

    setLastUpdated(Date.now()); // Trigger component updates
  }, []);

  // Get notifications by type
  const getNotificationsByType = useCallback(
    (type) => {
      return notifications.filter((n) => n.type === type);
    },
    [notifications]
  );

  // Get unread notifications
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter((n) => !n.isRead && !n.IsRead);
  }, [notifications]);

  // Clear all notifications (for logout)
  const clearNotifications = useCallback(() => {
    setNotifications([]);
    setUnreadCount(0);
    setLastUpdated(Date.now());
  }, []);

  const value = {
    // State
    notifications,
    unreadCount,
    loading,
    lastUpdated, // Include for components that need to track updates

    // Actions
    loadNotifications,
    forceRefresh, // Add this for manual refresh
    markAsRead,
    markAllAsRead,
    deleteNotification,
    addNotification,
    clearNotifications,

    // Getters
    getNotificationsByType,
    getUnreadNotifications,

    // Computed values
    hasUnread: unreadCount > 0,
    totalCount: notifications.length,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export default NotificationContext;
