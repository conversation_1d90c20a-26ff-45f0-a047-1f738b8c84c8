/**
 * Utility functions for blood request management
 */

/**
 * Get status text for blood request status code
 */
export const getStatusText = (status) => {
  switch (status) {
    case 0:
      return "Đang chờ xử lý"; // Pending
    case 1:
      return "Chấp nhận"; // Accepted
    case 2:
      return "<PERSON>àn thành"; // Completed
    case 3:
      return "Từ chối"; // Rejected
    default:
      return "Không xác định";
  }
};

/**
 * Get status color for blood request status code
 */
export const getStatusColor = (status) => {
  switch (status) {
    case 0:
      return "warning"; // Đang chờ xử lý - orange
    case 1:
      return "success"; // Chấp nhận - green
    case 2:
      return "info"; // Hoàn thành - blue
    case 3:
      return "danger"; // Từ chối - red
    default:
      return "secondary";
  }
};

/**
 * Filter blood requests based on filters
 */
export const filterBloodRequests = (requests, filters) => {
  if (!requests || !Array.isArray(requests)) {
    return [];
  }

  return requests.filter((request) => {
    // Filter by blood type - check multiple possible fields
    if (filters.bloodType !== "all") {
      let requestBloodType = "";

      // Try different ways to get blood type based on API structure
      if (request.bloodType) {
        requestBloodType = request.bloodType;
      } else if (request.bloodTypeDisplay) {
        requestBloodType = request.bloodTypeDisplay;
      } else if (request.bloodGroup && request.rhType) {
        // Convert from separate fields to combined format
        // Handle different rhType formats
        let rhSymbol = "";
        if (
          request.rhType === "Rh+" ||
          request.rhType === "+" ||
          request.rhType === "Positive"
        ) {
          rhSymbol = "+";
        } else if (
          request.rhType === "Rh-" ||
          request.rhType === "-" ||
          request.rhType === "Negative"
        ) {
          rhSymbol = "-";
        }
        requestBloodType = `${request.bloodGroup}${rhSymbol}`;
      }

      if (requestBloodType !== filters.bloodType) {
        return false;
      }
    }

    // Filter by component type - handle both componentType and componentId
    if (filters.componentType !== "all") {
      let requestComponentType = "";

      // Try different ways to get component type
      if (request.componentType) {
        requestComponentType = request.componentType;
      } else if (request.componentId) {
        // Map componentId to component type name if needed
        // This mapping should match your component types
        const componentMap = {
          1: "Toàn phần",
          2: "Hồng cầu",
          3: "Tiểu cầu",
          4: "Huyết tương",
          // Add more mappings as needed
        };
        requestComponentType = componentMap[request.componentId] || "Toàn phần";
      }

      if (requestComponentType !== filters.componentType) {
        return false;
      }
    }

    // Filter by status - handle both string and number values
    if (filters.status !== "all") {
      const filterStatus = parseInt(filters.status);
      const requestStatus = parseInt(request.status);

      if (requestStatus !== filterStatus) {
        return false;
      }
    }

    return true;
  });
};

/**
 * Get blood types for filter options
 */
export const getBloodTypes = () => [
  "A+",
  "A-",
  "B+",
  "B-",
  "AB+",
  "AB-",
  "O+",
  "O-",
];

/**
 * Get status options for filter
 */
export const getStatusOptions = () => [
  { value: "all", label: "Tất cả trạng thái" },
  { value: "0", label: "Đang chờ xử lý" },
  { value: "1", label: "Đã chấp nhận" },
  { value: "2", label: "Hoàn thành" },
  { value: "3", label: "Từ chối" },
];

/**
 * Format date for display
 */
export const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  try {
    return new Date(dateString).toLocaleString("vi-VN");
  } catch (error) {
    return "N/A";
  }
};
